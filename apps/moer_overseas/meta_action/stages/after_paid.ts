import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { DataService } from '../../helper/getter/get_data'
import { MetaAction } from '../meta_action'

export class AfterPaid extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    return await DataService.isPaidSystemCourse(chatId)
  }
  async getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return null
  }

  async getGuidance(chatId: string): Promise<string> {
    return ''
  }

  async getMetaAction(chatId: string): Promise<Record<string, string>> {
    return MetaAction.metaActionAfterPaid
  }

  async getThinkPrompt(chatId: string): Promise<string> {
    return MetaAction.thinkPromptAfterPaid
  }

  async prepareActivation(chatId: string, roundId: string): Promise<void> {
    return undefined
  }
}
